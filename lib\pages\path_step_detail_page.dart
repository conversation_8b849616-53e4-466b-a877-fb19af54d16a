import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import '../models/models.dart' as models;
import '../services/firestore.dart';
import '../theme/theme.dart';
import '../widgets/youtube_video_resource_widget.dart';
import '../widgets/generic_resource_widget.dart';
import '../widgets/chat_card.dart';
import 'persona_selection_page.dart';

class PathStepDetailPage extends StatefulWidget {
  final models.PathStep pathStep;
  final models.GuidedPath guidedPath;
  final models.UserPathProgress? userProgress;

  const PathStepDetailPage({
    super.key,
    required this.pathStep,
    required this.guidedPath,
    this.userProgress,
  });

  @override
  State<PathStepDetailPage> createState() => _PathStepDetailPageState();
}

class _PathStepDetailPageState extends State<PathStepDetailPage> {
  bool _isCompleting = false;
  models.User? _currentUser;
  models.Chat? _stepChat;
  models.SystemPersona? _stepChatPersona;
  bool _isLoadingChat = false;

  bool get _isCompleted =>
      widget.userProgress?.isStepCompleted(widget.pathStep.stepNumber) ?? false;

  bool get _hasStepChat =>
      widget.userProgress?.hasStepChatId(widget.pathStep.stepNumber) ?? false;

  String? get _stepChatId =>
      widget.userProgress?.getStepChatId(widget.pathStep.stepNumber);
  bool get _isCurrentStep =>
      widget.userProgress?.currentStepNumber == widget.pathStep.stepNumber;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
    // Load the step chat if it exists
    if (_hasStepChat) {
      _loadStepChat();
    }
  }

  Future<void> _loadCurrentUser() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        final user = await FirestoreService.getUser(currentUser.uid);
        setState(() {
          _currentUser = user;
        });
      }
    } catch (e) {
      // Handle error silently, user will just not have admin privileges
    }
  }

  /// Determines if the current user can access this step
  bool get _canAccessStep {
    // Admin users can access any step
    if (_currentUser?.isAdmin == true) {
      return true;
    }

    // If no progress exists, only the first step is accessible
    if (widget.userProgress == null) {
      return widget.pathStep.stepNumber == 1;
    }

    // If step is already completed, it's accessible
    if (_isCompleted) {
      return true;
    }

    // If it's the current step, it's accessible
    if (_isCurrentStep) {
      return true;
    }

    // Otherwise, it's not accessible
    return false;
  }

  Future<void> _completeStep() async {
    if (_isCompleted) return;

    setState(() {
      _isCompleting = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      // Update the legacy completion tracking
      await FirestoreService.completeUserPathStep(
        currentUser.uid,
        widget.guidedPath.id!,
        widget.pathStep.stepNumber,
      );

      // Also update the new step progress tracking if we have userProgress
      if (widget.userProgress != null) {
        final updatedProgress = widget.userProgress!.markStepCompleted(
          widget.pathStep.stepNumber,
        );
        await FirestoreService.createOrUpdateUserPathProgress(updatedProgress);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Step ${widget.pathStep.stepNumber} completed!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(); // Return to path detail
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to complete step: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isCompleting = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Step ${widget.pathStep.stepNumber}'),
        actions: [
          if (_isCompleted) Icon(AppIcons.completed, color: AppColors.success),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildContent(),
            _buildCompletionCriteria(),
            _buildChatSection(),
            if (widget.pathStep.resources?.isNotEmpty == true)
              _buildResources(),
            if (widget.pathStep.reflectionPrompts?.isNotEmpty == true)
              _buildReflectionPrompts(),
            const SizedBox(height: 100), // Space for bottom bar
          ],
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildHeader() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step indicator and path info
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getCategoryColor(),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  widget.guidedPath.category,
                  style: theme.textTheme.labelMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${widget.pathStep.stepNumber} of ${widget.guidedPath.stepCount}',
                style: theme.textTheme.labelLarge?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Title
          Text(
            widget.pathStep.title,
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 8),

          // Duration
          if (widget.pathStep.estimatedDurationMinutes != null)
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 16,
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
                const SizedBox(width: 4),
                Text(
                  'Estimated time: ${widget.pathStep.estimatedDurationMinutes} minutes',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Description',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(widget.pathStep.description, style: theme.textTheme.bodyLarge),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildCompletionCriteria() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.task_alt, color: colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Completion Criteria',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.pathStep.completionCriteria,
            style: theme.textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildChatSection() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.chat_bubble_outline,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'AI Coach Chat',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_hasStepChat)
            _buildExistingChatCard()
          else
            _buildStartChatButton(),
        ],
      ),
    );
  }

  Widget _buildStartChatButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Get personalized guidance for this step by chatting with an AI coach.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _isLoadingChat ? null : _startChat,
            icon: _isLoadingChat
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.chat),
            label: Text(_isLoadingChat ? 'Starting Chat...' : 'Start Chat'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildExistingChatCard() {
    if (_stepChat == null) {
      // Load the chat if we have a chatId but haven't loaded the chat yet
      _loadStepChat();
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Continue your conversation with your AI coach for this step.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(
              context,
            ).colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
        const SizedBox(height: 12),
        ChatCard(
          chat: _stepChat!,
          systemPersona: _stepChatPersona,
          onRefresh: () {
            // Refresh callback if needed
          },
        ),
      ],
    );
  }

  Widget _buildResources() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Resources',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.pathStep.resources!.map(
            (resource) => _buildResourceItem(resource),
          ),
        ],
      ),
    );
  }

  Widget _buildResourceItem(models.ExternalResource resource) {
    // Check if this is a video resource with a YouTube URL
    if (resource.type == models.ExternalResourceType.video &&
        _isYouTubeUrl(resource.link)) {
      return YouTubeVideoResourceWidget(resource: resource);
    }

    // For all other resource types, use the generic resource widget
    return GenericResourceWidget(resource: resource);
  }

  /// Check if the given URL is a YouTube URL
  bool _isYouTubeUrl(String url) {
    try {
      final videoId = YoutubePlayerController.convertUrlToId(url);
      return videoId != null;
    } catch (e) {
      return false;
    }
  }

  Widget _buildReflectionPrompts() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.psychology, color: colorScheme.secondary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Reflection Prompts',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...widget.pathStep.reflectionPrompts!.asMap().entries.map(
            (entry) => _buildReflectionPrompt(entry.key + 1, entry.value),
          ),
        ],
      ),
    );
  }

  Widget _buildReflectionPrompt(int index, String prompt) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: colorScheme.secondary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$index',
                style: theme.textTheme.labelSmall?.copyWith(
                  color: colorScheme.onSecondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              prompt,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _startChat() async {
    setState(() {
      _isLoadingChat = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Navigate to persona selection page and wait for result
      final selectedPersonaId = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) =>
              const PersonaSelectionPage(returnPersonaIdOnly: true),
        ),
      );

      if (selectedPersonaId == null) {
        // User cancelled persona selection
        return;
      }

      // Create new chat with selected persona
      final chatId = await FirestoreService.createChat(
        currentUser.uid,
        systemPersonaId: selectedPersonaId,
        title: 'Step ${widget.pathStep.stepNumber}: ${widget.pathStep.title}',
      );

      // Update user path progress with the new chat ID
      if (widget.userProgress != null) {
        final updatedProgress = widget.userProgress!.setStepChatId(
          widget.pathStep.stepNumber,
          chatId,
        );
        await FirestoreService.createOrUpdateUserPathProgress(updatedProgress);
      }

      // Load the created chat
      await _loadStepChat();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Chat started! You can now get guidance for this step.',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start chat: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingChat = false;
        });
      }
    }
  }

  Future<void> _loadStepChat() async {
    final chatId = _stepChatId;
    if (chatId == null) return;

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) return;

      final chat = await FirestoreService.getChat(currentUser.uid, chatId);
      if (chat != null) {
        final persona = await FirestoreService.getSystemPersona(
          chat.systemPersonaId,
        );

        if (mounted) {
          setState(() {
            _stepChat = chat;
            _stepChatPersona = persona;
          });
        }
      }
    } catch (e) {
      debugPrint('Failed to load step chat: $e');
    }
  }

  Widget _buildBottomBar() {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(child: _buildActionButton()),
    );
  }

  Widget _buildActionButton() {
    if (_isCompleted) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: null,
          icon: const Icon(Icons.check_circle),
          label: const Text('Step Completed'),
          style: ElevatedButton.styleFrom(
            padding: AppDimensions.paddingVerticalM,
            backgroundColor: AppColors.success,
            foregroundColor: AppColors.onPrimary,
          ),
        ),
      );
    }

    if (!_canAccessStep) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text('Complete previous steps first'),
        ),
      );
    }

    // Only show "Mark as Complete" button if the step has an associated chat
    if (!_hasStepChat) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: null,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
          ),
          child: const Text('Start a chat to complete this step'),
        ),
      );
    }

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isCompleting ? null : _completeStep,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: _isCompleting
            ? const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 8),
                  Text('Completing...'),
                ],
              )
            : const Text('Mark as Complete'),
      ),
    );
  }

  Color _getCategoryColor() {
    return AppColors.getCategoryColor(widget.guidedPath.category);
  }
}
